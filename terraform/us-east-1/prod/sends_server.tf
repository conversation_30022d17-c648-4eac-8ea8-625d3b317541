locals {
  subnet_ids_list         = tolist(data.aws_subnets.default_subnets.ids)
  subnet_ids_random_index = random_id.index.dec % length(data.aws_subnets.default_subnets.ids)
  instance_subnet_id      = local.subnet_ids_list[local.subnet_ids_random_index]
}
data "aws_ami" "sends_server" {
  most_recent = true

  filter {
    name   = "name"
    values = ["thankview-sends-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["019190539304"]
}

data "aws_iam_policy_document" "sendsserver_sqs_access" {
  statement {
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      module.mail_sqs_queue.queue_arn,
      module.small_mail_sqs_queue.queue_arn,
      module.thankview-sms-queue.queue_arn
    ]
  }
}

resource "aws_iam_role" "sendsserver" {
  name               = "sends-server-role-prod"
  assume_role_policy = data.aws_iam_policy_document.webserver_assume_role.json
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy",
    "arn:aws:iam::aws:policy/AmazonEC2FullAccess",
    "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
  ]

  inline_policy {
    name   = "sendsserver-sqs-access"
    policy = data.aws_iam_policy_document.sendsserver_sqs_access.json
  }

  inline_policy {
    name   = "sendsserver-secrets-access"
    policy = data.aws_iam_policy_document.webserver_secrets_access.json
  }
}

resource "aws_iam_instance_profile" "sendsserver" {
  name = "sendsserver-prod"
  role = aws_iam_role.sendsserver.name
}

resource "aws_launch_template" "sends_conf" {
  name_prefix   = "thankview-sends-server-"
  image_id      = data.aws_ami.sends_server.id
  instance_type = "t3a.large"
  ebs_optimized = true


  iam_instance_profile {
    name = aws_iam_instance_profile.sendsserver.name
  }
  vpc_security_group_ids = [aws_security_group.thankview-ssh-access.id, aws_security_group.sends-server.id]

  user_data = base64encode(templatefile(
    "./config/sends-server-cloudinit.yaml",
    {
      digicert_ca_encoded                      = filebase64("./config/DigiCertSHA2SecureServerCA.crt.pem")
      elasticsearch_ca_encoded                 = filebase64("./config/elasticsearch-ca.pem")
      sends_worker_supervisor_conf_encoded     = filebase64("./config/sends-worker-supervisor.conf")
      sends_sms_worker_supervisor_conf_encoded = filebase64("./config/sends-sms-worker-supervisor.conf")
      datadog_api_key                          = data.aws_ssm_parameter.datadog_api_key.value #datadog SSM parameter value
    }
  ))

  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_size           = 32    # Match this to your AMI's volume size if you want to keep it
      delete_on_termination = true  # Set to true to ensure the volume is deleted upon instance termination
      volume_type           = "gp3" # Specify your desired volume type, matching AMI or as needed
      encrypted             = true  # Set according to your encryption needs
    }
  }

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name             = "thankview-sends-server-static",
      VantaDescription = "Send worker - static",
      VantaOwner       = "<EMAIL>",
      VantaProd        = "true"
      Team             = "devops"
      Service          = "sendserver"
      Environment      = "prod"
      APP_ROLE         = "sends-worker"

    }
  }

  tag_specifications {
    resource_type = "volume"
    tags = {
      Name = "thankview-sends-server-static"
    }
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_autoscaling_group" "sends_static_autoscaling" {
  name             = "thankview-static-sends-servers"
  min_size         = 4
  max_size         = 10
  desired_capacity = 4
  availability_zones = [
    "us-east-1b",
    "us-east-1c",
    "us-east-1d"
  ]
  enabled_metrics = [
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity",
    "GroupDesiredCapacity",
    "GroupInServiceCapacity",
    "GroupInServiceInstances",
    "GroupMaxSize",
    "GroupMinSize",
    "GroupPendingCapacity",
    "GroupPendingInstances",
    "GroupStandbyCapacity",
    "GroupStandbyInstances",
    "GroupTerminatingCapacity",
    "GroupTerminatingInstances",
    "GroupTotalCapacity",
    "GroupTotalInstances",
    "WarmPoolDesiredCapacity",
    "WarmPoolMinSize",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "WarmPoolWarmedCapacity",
  ]

  launch_template {
    id      = aws_launch_template.sends_conf.id
    version = "$Latest"
  }

  instance_refresh {
    strategy = "Rolling"
    preferences {
      instance_warmup = 120
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      max_size,
      desired_capacity
    ]
  }
}


resource "aws_autoscaling_policy" "scale_up" {
  name                   = "HighCPU"
  scaling_adjustment     = 2
  adjustment_type        = "ChangeInCapacity"
  cooldown               = 900
  autoscaling_group_name = aws_autoscaling_group.sends_static_autoscaling.name
}

resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name          = "ASG-HighCPU-thankview-static-sends-servers"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 70
  alarm_description   = "This Alarm triggers when the collective CPU in thankview-static-sends-servers autoscaling group is over 70%."
  alarm_actions       = [aws_autoscaling_policy.scale_up.arn, data.aws_sns_topic.existing_topic.arn]

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.sends_static_autoscaling.name
  }
}

